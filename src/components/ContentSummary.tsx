'use client';

import { useState } from 'react';

interface MigrationData {
  suffix: string;
  summary?: Record<string, number>;
  [key: string]: unknown;
}

interface StaticGenResult {
  success: boolean;
  message: string;
  downloadUrl?: string;
}

interface ContentSummaryProps {
  migrationData: MigrationData;
  onReset: () => void;
}

export default function ContentSummary({ migrationData, onReset }: ContentSummaryProps) {
  const [isGeneratingStatic, setIsGeneratingStatic] = useState(false);
  const [staticGenResult, setStaticGenResult] = useState<StaticGenResult | null>(null);

  const generateStaticSite = async () => {
    setIsGeneratingStatic(true);
    setStaticGenResult(null);

    try {
      const response = await fetch('/api/static/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          suffix: migrationData.suffix
        }),
      });

      const result = await response.json();
      setStaticGenResult(result);
    } catch {
      setStaticGenResult({
        success: false,
        message: 'Failed to generate static site'
      });
    } finally {
      setIsGeneratingStatic(false);
    }
  };

  const summary = migrationData?.summary || {};

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
          Migration Complete!
        </h2>
        <button
          type="button"
          onClick={onReset}
          className="px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
        >
          New Migration
        </button>
      </div>

      {/* Success Message */}
      <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <p className="text-green-800 dark:text-green-200 font-medium">
            Your WordPress content has been successfully migrated to Supabase!
          </p>
        </div>
      </div>

      {/* Migration Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center">
          <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">
            {summary[`wp_posts_${migrationData.suffix}`] || 0}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Posts Migrated</div>
        </div>

        <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg text-center">
          <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-1">
            {summary[`wp_pages_${migrationData.suffix}`] || 0}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Pages Migrated</div>
        </div>

        <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg text-center">
          <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-1">
            {summary[`wp_media_${migrationData.suffix}`] || 0}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Media Files</div>
        </div>

        <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg text-center">
          <div className="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-1">
            {summary.media_files || 0}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Files in Storage</div>
        </div>
      </div>

      {/* Database Information */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
          Database Information
        </h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Table Suffix:</span>
            <span className="font-mono text-gray-900 dark:text-white">{migrationData.suffix}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Posts Table:</span>
            <span className="font-mono text-gray-900 dark:text-white">wp_posts_{migrationData.suffix}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Pages Table:</span>
            <span className="font-mono text-gray-900 dark:text-white">wp_pages_{migrationData.suffix}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Media Table:</span>
            <span className="font-mono text-gray-900 dark:text-white">wp_media_{migrationData.suffix}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Storage Bucket:</span>
            <span className="font-mono text-gray-900 dark:text-white">wp-media-{migrationData.suffix}</span>
          </div>
        </div>
      </div>

      {/* Supabase Access */}
      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <h3 className="text-lg font-medium text-blue-800 dark:text-blue-200 mb-3">
          Access Your Data in Supabase
        </h3>
        <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
          Your migrated content is now available in your Supabase project. You can access it through:
        </p>
        <div className="space-y-2">
          <a
            href={`${process.env.NEXT_PUBLIC_SUPABASE_URL}/project/default/editor`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-3 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors mr-2"
          >
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            Supabase Table Editor
          </a>
          <a
            href={`${process.env.NEXT_PUBLIC_SUPABASE_URL}/project/default/storage/buckets`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-3 py-2 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
          >
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            Storage Buckets
          </a>
        </div>
      </div>

      {/* Static Site Generation */}
      <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-3">
          Generate Static Site (Optional)
        </h3>
        <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-4">
          Generate static HTML files from your migrated content for deployment on Vercel, Netlify, or other static hosting platforms.
        </p>
        
        <button
          type="button"
          onClick={generateStaticSite}
          disabled={isGeneratingStatic}
          className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-400 text-white rounded-md transition-colors"
        >
          {isGeneratingStatic ? 'Generating...' : 'Generate Static Site'}
        </button>

        {staticGenResult && (
          <div className={`mt-4 p-3 rounded-md ${
            staticGenResult.success 
              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' 
              : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
          }`}>
            <p className={`text-sm ${
              staticGenResult.success 
                ? 'text-green-800 dark:text-green-200' 
                : 'text-red-800 dark:text-red-200'
            }`}>
              {staticGenResult.message}
            </p>
            {staticGenResult.success && staticGenResult.downloadUrl && (
              <a
                href={staticGenResult.downloadUrl}
                className="inline-flex items-center mt-2 px-3 py-1 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
              >
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                Download Static Site
              </a>
            )}
          </div>
        )}
      </div>

      {/* Next Steps */}
      <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
          Next Steps
        </h3>
        <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
          <li className="flex items-start">
            <span className="text-blue-500 mr-2">•</span>
            Review your migrated content in the Supabase dashboard
          </li>
          <li className="flex items-start">
            <span className="text-blue-500 mr-2">•</span>
            Set up Row Level Security (RLS) policies for your tables
          </li>
          <li className="flex items-start">
            <span className="text-blue-500 mr-2">•</span>
            Configure your application to use the new Supabase data
          </li>
          <li className="flex items-start">
            <span className="text-blue-500 mr-2">•</span>
            Test all functionality with the migrated content
          </li>
          <li className="flex items-start">
            <span className="text-blue-500 mr-2">•</span>
            Deploy your static site if generated
          </li>
        </ul>
      </div>
    </div>
  );
}
