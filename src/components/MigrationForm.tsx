'use client';

import { useState } from 'react';

interface ConnectionResult {
  success: boolean;
  message: string;
}

interface DiscoveredContent {
  success: boolean;
  message?: string;
  content?: {
    posts: number;
    pages: number;
    media: number;
    categories: number;
    tags: number;
    total: number;
  };
}

interface MigrationData {
  wpConfig: {
    wpSite: string;
    wpUsername: string;
    wpPassword: string;
  };
  discoveredContent: DiscoveredContent['content'];
}

interface MigrationFormProps {
  onMigrationStart: (data: MigrationData) => void;
}

export default function MigrationForm({ onMigrationStart }: MigrationFormProps) {
  const [formData, setFormData] = useState({
    wpSite: 'https://q8hotel.co.uk',
    wpUsername: 'mywebmasteruk',
    wpPassword: 'B8Sb 76kl UYuY 0H8V 6tJ6 Bocn'
  });

  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionResult, setConnectionResult] = useState<ConnectionResult | null>(null);
  const [discoveredContent, setDiscoveredContent] = useState<DiscoveredContent | null>(null);
  const [isDiscovering, setIsDiscovering] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Reset connection status when form changes
    setConnectionResult(null);
    setDiscoveredContent(null);
  };

  const testConnection = async () => {
    setIsConnecting(true);
    setConnectionResult(null);

    try {
      const response = await fetch('/api/wordpress/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();
      setConnectionResult(result);

      if (result.success) {
        // Automatically discover content after successful connection
        discoverContent();
      }
    } catch {
      setConnectionResult({
        success: false,
        message: 'Failed to connect to WordPress site'
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const discoverContent = async () => {
    setIsDiscovering(true);
    setDiscoveredContent(null);

    try {
      const response = await fetch('/api/wordpress/discover', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();
      setDiscoveredContent(result);
    } catch {
      setDiscoveredContent({
        success: false,
        message: 'Failed to discover content'
      });
    } finally {
      setIsDiscovering(false);
    }
  };

  const startMigration = async () => {
    if (!connectionResult?.success || !discoveredContent?.success) {
      alert('Please test connection and discover content first');
      return;
    }

    onMigrationStart({
      wpConfig: formData,
      discoveredContent: discoveredContent.content!
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
        WordPress Connection
      </h2>

      <div className="space-y-4">
        <div>
          <label htmlFor="wpSite" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            WordPress Site URL
          </label>
          <input
            type="url"
            id="wpSite"
            name="wpSite"
            value={formData.wpSite}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="https://your-wordpress-site.com"
            required
          />
        </div>

        <div>
          <label htmlFor="wpUsername" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            WordPress Admin Username
          </label>
          <input
            type="text"
            id="wpUsername"
            name="wpUsername"
            value={formData.wpUsername}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            required
          />
        </div>

        <div>
          <label htmlFor="wpPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            WordPress App Password
          </label>
          <input
            type="password"
            id="wpPassword"
            name="wpPassword"
            value={formData.wpPassword}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            required
          />
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Generate an App Password in your WordPress admin under Users → Profile
          </p>
        </div>

        <button
          type="button"
          onClick={testConnection}
          disabled={isConnecting}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
        >
          {isConnecting ? 'Connecting...' : 'Connect to WordPress'}
        </button>

        {connectionResult && (
          <div className={`p-4 rounded-md ${
            connectionResult.success 
              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' 
              : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
          }`}>
            <p className={`text-sm ${
              connectionResult.success 
                ? 'text-green-800 dark:text-green-200' 
                : 'text-red-800 dark:text-red-200'
            }`}>
              {connectionResult.message}
            </p>
          </div>
        )}

        {isDiscovering && (
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              Discovering content...
            </p>
          </div>
        )}

        {discoveredContent && (
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Discovered Content
            </h3>
            {discoveredContent.success ? (
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {discoveredContent.content.posts}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Posts</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {discoveredContent.content.pages}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Pages</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {discoveredContent.content.media}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Media</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {discoveredContent.content.categories}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Categories</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                    {discoveredContent.content.tags}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Tags</div>
                </div>
              </div>
            ) : (
              <p className="text-red-600 dark:text-red-400">
                {discoveredContent.message}
              </p>
            )}
          </div>
        )}

        {connectionResult?.success && discoveredContent?.success && (
          <button
            type="button"
            onClick={startMigration}
            className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-md transition-colors text-lg"
          >
            Start Migration ({discoveredContent.content.total} items)
          </button>
        )}
      </div>
    </div>
  );
}
