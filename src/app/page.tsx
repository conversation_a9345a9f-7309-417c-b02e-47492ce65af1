'use client';

import { useState } from 'react';
import Link from 'next/link';

type MigrationStep = 'form' | 'progress' | 'summary';

export default function Home() {
  const [currentStep, setCurrentStep] = useState<MigrationStep>('form');

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            WordPress to Supabase Migrator
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Migrate your WordPress content to Supabase with ease
          </p>
        </header>

        <div className="max-w-4xl mx-auto">
          {currentStep === 'form' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                WordPress to Supabase Migration
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                This application will help you migrate your WordPress content to Supabase.
              </p>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h3 className="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
                    🚀 Getting Started
                  </h3>
                  <ol className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                    <li>1. Configure your Supabase settings</li>
                    <li>2. Enter your WordPress credentials</li>
                    <li>3. Test the connection</li>
                    <li>4. Start the migration</li>
                  </ol>
                </div>

                <div className="flex gap-4">
                  <Link
                    href="/settings"
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors text-center"
                  >
                    Configure Supabase Settings
                  </Link>
                  <button
                    onClick={() => setCurrentStep('form')}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-md transition-colors"
                  >
                    Start Migration
                  </button>
                </div>
              </div>
            </div>
          )}

          {currentStep === 'progress' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                Migration in Progress
              </h2>
              <p>Migration functionality will be implemented here.</p>
            </div>
          )}

          {currentStep === 'summary' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                Migration Complete
              </h2>
              <p>Migration summary will be shown here.</p>
            </div>
          )}
        </div>

        <footer className="text-center mt-16 text-gray-500 dark:text-gray-400">
          <p>Built with Next.js, Supabase, and the WordPress REST API</p>
        </footer>
      </div>
    </div>
  );
}
