'use client';

import { useState } from 'react';
import MigrationForm from '@/components/MigrationForm';
import ProgressTracker from '@/components/ProgressTracker';
import ContentSummary from '@/components/ContentSummary';

interface SaveResult {
  success: boolean;
  message: string;
  connectionTest?: {
    success: boolean;
    message?: string;
  };
}

interface MigrationData {
  wpConfig: {
    wpSite: string;
    wpUsername: string;
    wpPassword: string;
  };
  discoveredContent: Record<string, unknown>;
}

type MigrationStep = 'setup' | 'form' | 'progress' | 'summary';

export default function Home() {
  const [currentStep, setCurrentStep] = useState<MigrationStep>('setup');
  const [migrationData, setMigrationData] = useState<MigrationData | null>(null);

  // Supabase settings state
  const [supabaseSettings, setSupabaseSettings] = useState({
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    supabaseServiceKey: '',
    supabaseAccessToken: ''
  });
  const [isSavingSupabase, setIsSavingSupabase] = useState(false);
  const [supabaseSaveResult, setSupabaseSaveResult] = useState<SaveResult | null>(null);

  const handleSupabaseSettingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSupabaseSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const saveSupabaseSettings = async () => {
    setIsSavingSupabase(true);
    setSupabaseSaveResult(null);

    try {
      const response = await fetch('/api/supabase/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(supabaseSettings),
      });

      const result = await response.json();
      setSupabaseSaveResult(result);
    } catch {
      setSupabaseSaveResult({
        success: false,
        message: 'Failed to save settings'
      });
    } finally {
      setIsSavingSupabase(false);
    }
  };

  const handleMigrationStart = (data: MigrationData) => {
    setMigrationData(data);
    setCurrentStep('progress');
  };

  const handleMigrationComplete = (summary: Record<string, unknown>) => {
    setMigrationData(prev => prev ? { ...prev, summary } : null);
    setCurrentStep('summary');
  };

  const resetMigration = () => {
    setCurrentStep('setup');
    setMigrationData(null);
    setSupabaseSaveResult(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            WordPress to Supabase Migrator
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Migrate your WordPress content to Supabase with ease
          </p>
        </header>

        {currentStep === 'setup' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* Supabase Configuration */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                1. Configure Supabase Settings
              </h2>

              <div className="space-y-4">
                <div>
                  <label htmlFor="supabaseUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Supabase URL
                  </label>
                  <input
                    type="url"
                    id="supabaseUrl"
                    name="supabaseUrl"
                    value={supabaseSettings.supabaseUrl}
                    onChange={handleSupabaseSettingsChange}
                    placeholder="https://your-project.supabase.co"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="supabaseServiceKey" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Service Role Key
                  </label>
                  <input
                    type="password"
                    id="supabaseServiceKey"
                    name="supabaseServiceKey"
                    value={supabaseSettings.supabaseServiceKey}
                    onChange={handleSupabaseSettingsChange}
                    placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    required
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Found in your Supabase project settings under API
                  </p>
                </div>

                <div>
                  <label htmlFor="supabaseAccessToken" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Access Token (Optional)
                  </label>
                  <input
                    type="password"
                    id="supabaseAccessToken"
                    name="supabaseAccessToken"
                    value={supabaseSettings.supabaseAccessToken}
                    onChange={handleSupabaseSettingsChange}
                    placeholder="sbp_..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Personal access token for advanced operations
                  </p>
                </div>

                <button
                  onClick={saveSupabaseSettings}
                  disabled={isSavingSupabase || !supabaseSettings.supabaseUrl || !supabaseSettings.supabaseServiceKey}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                  {isSavingSupabase ? 'Testing Connection...' : 'Test & Save Supabase Settings'}
                </button>

                {supabaseSaveResult && (
                  <div className={`p-4 rounded-md ${supabaseSaveResult.success ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'}`}>
                    <p className={`text-sm ${supabaseSaveResult.success ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}`}>
                      {supabaseSaveResult.message}
                    </p>
                    {supabaseSaveResult.connectionTest && (
                      <p className={`text-xs mt-1 ${supabaseSaveResult.connectionTest.success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        Connection test: {supabaseSaveResult.connectionTest.message || (supabaseSaveResult.connectionTest.success ? 'Success' : 'Failed')}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* WordPress Configuration */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                2. Configure WordPress Connection
              </h2>

              <div className="h-full">
                <MigrationForm onMigrationStart={handleMigrationStart} />
              </div>
            </div>
          </div>
        )}

        {currentStep === 'progress' && migrationData && (
          <ProgressTracker
            migrationData={migrationData}
            onComplete={handleMigrationComplete}
            onReset={resetMigration}
          />
        )}

        {currentStep === 'summary' && migrationData && (
          <ContentSummary
            migrationData={migrationData}
            onReset={resetMigration}
          />
        )}

        {currentStep === 'setup' && (
          <div className="mt-8 text-center">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 max-w-2xl mx-auto">
              <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-3">Migration Process:</h3>
              <div className="text-sm text-blue-700 dark:text-blue-300 space-y-2">
                <p>1. Configure your Supabase connection settings above</p>
                <p>2. Test your WordPress site connection</p>
                <p>3. Review discovered content and start migration</p>
                <p>4. Monitor progress and download results</p>
              </div>
            </div>
          </div>
        )}

        <footer className="text-center mt-16 text-gray-500 dark:text-gray-400">
          <p>Built with Next.js, Supabase, and the WordPress REST API</p>
        </footer>
      </div>
    </div>
  );
}
