import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase.js';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

export async function POST(request) {
  try {
    const { suffix } = await request.json();

    if (!suffix) {
      return NextResponse.json({
        success: false,
        message: 'Missing suffix parameter'
      }, { status: 400 });
    }

    // Create output directory
    const outputDir = path.join(process.cwd(), 'static-output', suffix);
    await mkdir(outputDir, { recursive: true });

    // Fetch posts and pages from Supabase
    const [postsResult, pagesResult] = await Promise.all([
      supabaseAdmin.from(`wp_posts_${suffix}`).select('*'),
      supabaseAdmin.from(`wp_pages_${suffix}`).select('*')
    ]);

    if (postsResult.error || pagesResult.error) {
      throw new Error('Failed to fetch content from Supabase');
    }

    const posts = postsResult.data || [];
    const pages = pagesResult.data || [];

    // Generate HTML files
    let generatedFiles = 0;

    // Generate index.html
    const indexHtml = generateIndexPage(posts, pages);
    await writeFile(path.join(outputDir, 'index.html'), indexHtml);
    generatedFiles++;

    // Generate individual post pages
    for (const post of posts) {
      const postHtml = generatePostPage(post);
      const postDir = path.join(outputDir, 'posts');
      await mkdir(postDir, { recursive: true });
      await writeFile(path.join(postDir, `${post.slug}.html`), postHtml);
      generatedFiles++;
    }

    // Generate individual page files
    for (const page of pages) {
      const pageHtml = generatePageFile(page);
      await writeFile(path.join(outputDir, `${page.slug}.html`), pageHtml);
      generatedFiles++;
    }

    // Generate CSS file
    const cssContent = generateCSS();
    await writeFile(path.join(outputDir, 'styles.css'), cssContent);
    generatedFiles++;

    return NextResponse.json({
      success: true,
      message: `Static site generated successfully with ${generatedFiles} files`,
      outputPath: outputDir,
      stats: {
        posts: posts.length,
        pages: pages.length,
        totalFiles: generatedFiles
      }
    });

  } catch (error) {
    console.error('Static generation error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to generate static site'
    }, { status: 500 });
  }
}

function generateIndexPage(posts, pages) {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migrated WordPress Site</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>Welcome to Your Migrated Site</h1>
        <nav>
            <ul>
                ${pages.map(page => `<li><a href="${page.slug}.html">${page.title}</a></li>`).join('')}
            </ul>
        </nav>
    </header>
    
    <main>
        <section class="posts">
            <h2>Latest Posts</h2>
            ${posts.slice(0, 10).map(post => `
                <article class="post-preview">
                    <h3><a href="posts/${post.slug}.html">${post.title}</a></h3>
                    <div class="post-meta">
                        <time datetime="${post.created_at}">${new Date(post.created_at).toLocaleDateString()}</time>
                    </div>
                    <div class="post-excerpt">
                        ${post.excerpt || post.content.substring(0, 200) + '...'}
                    </div>
                </article>
            `).join('')}
        </section>
    </main>
    
    <footer>
        <p>Migrated from WordPress to Static Site</p>
    </footer>
</body>
</html>`;
}

function generatePostPage(post) {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${post.title}</title>
    <link rel="stylesheet" href="../styles.css">
</head>
<body>
    <header>
        <nav>
            <a href="../index.html">← Back to Home</a>
        </nav>
    </header>
    
    <main>
        <article class="post">
            <header class="post-header">
                <h1>${post.title}</h1>
                <div class="post-meta">
                    <time datetime="${post.created_at}">${new Date(post.created_at).toLocaleDateString()}</time>
                    ${post.categories && post.categories.length > 0 ? `
                        <div class="categories">
                            Categories: ${post.categories.join(', ')}
                        </div>
                    ` : ''}
                    ${post.tags && post.tags.length > 0 ? `
                        <div class="tags">
                            Tags: ${post.tags.join(', ')}
                        </div>
                    ` : ''}
                </div>
            </header>
            
            <div class="post-content">
                ${post.content}
            </div>
        </article>
    </main>
    
    <footer>
        <p>Migrated from WordPress to Static Site</p>
    </footer>
</body>
</html>`;
}

function generatePageFile(page) {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${page.title}</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <nav>
            <a href="index.html">← Back to Home</a>
        </nav>
    </header>
    
    <main>
        <article class="page">
            <header class="page-header">
                <h1>${page.title}</h1>
            </header>
            
            <div class="page-content">
                ${page.content}
            </div>
        </article>
    </main>
    
    <footer>
        <p>Migrated from WordPress to Static Site</p>
    </footer>
</body>
</html>`;
}

function generateCSS() {
  return `/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/* Header */
header {
    background-color: #f8f9fa;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

header h1 {
    text-align: center;
    margin-bottom: 1rem;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

nav a {
    text-decoration: none;
    color: #007bff;
    font-weight: 500;
}

nav a:hover {
    text-decoration: underline;
}

/* Main Content */
main {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* Posts */
.post-preview {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.post-preview h3 {
    margin-bottom: 0.5rem;
}

.post-preview h3 a {
    text-decoration: none;
    color: #333;
}

.post-preview h3 a:hover {
    color: #007bff;
}

.post-meta {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.post-excerpt {
    color: #495057;
}

/* Individual Post/Page */
.post, .page {
    max-width: 100%;
}

.post-header, .page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.post-content, .page-content {
    line-height: 1.8;
}

.post-content img, .page-content img {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
}

.categories, .tags {
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

/* Footer */
footer {
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
    border-top: 1px solid #e9ecef;
    color: #6c757d;
}

/* Responsive */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        gap: 1rem;
    }
    
    main {
        margin: 1rem auto;
    }
}`;
}
