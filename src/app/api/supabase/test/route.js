import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request) {
  try {
    const { supabaseUrl, supabaseServiceKey } = await request.json();

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({
        success: false,
        message: 'Missing Supabase URL or Service Key'
      }, { status: 400 });
    }

    // Test connection
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Try to query a simple table or perform a basic operation
    const { data, error } = await supabase
      .from('migration_jobs')
      .select('count', { count: 'exact', head: true });

    if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist, which is OK
      return NextResponse.json({
        success: false,
        message: `Connection failed: ${error.message}`
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful'
    });

  } catch (error) {
    console.error('Supabase test error:', error);
    return NextResponse.json({
      success: false,
      message: 'Connection test failed'
    }, { status: 500 });
  }
}
