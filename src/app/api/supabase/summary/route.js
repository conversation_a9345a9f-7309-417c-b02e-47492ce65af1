import { NextResponse } from 'next/server';
import { getMigrationSummary } from '@/lib/supabase.js';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const suffix = searchParams.get('suffix');

    if (!suffix) {
      return NextResponse.json({
        success: false,
        message: 'Missing suffix parameter'
      }, { status: 400 });
    }

    const summary = await getMigrationSummary(suffix);

    return NextResponse.json({
      success: true,
      summary
    });

  } catch (error) {
    console.error('Migration summary error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
