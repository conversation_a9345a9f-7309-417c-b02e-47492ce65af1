import { NextResponse } from 'next/server';
import { testWordPressConnection } from '@/lib/wordpress.js';

export async function POST(request) {
  try {
    const { wpSite, wpUsername, wpPassword } = await request.json();

    if (!wpSite || !wpUsername || !wpPassword) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields: wpSite, wpUsername, wpPassword'
      }, { status: 400 });
    }

    const result = await testWordPressConnection(wpSite, wpUsername, wpPassword);

    return NextResponse.json(result);
  } catch (error) {
    console.error('WordPress connection error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
