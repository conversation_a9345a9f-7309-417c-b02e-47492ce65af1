import { NextResponse } from 'next/server';
import { discoverWordPressContent } from '@/lib/wordpress.js';

export async function POST(request) {
  try {
    const { wpSite, wpUsername, wpPassword } = await request.json();

    if (!wpSite || !wpUsername || !wpPassword) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields: wpSite, wpUsername, wpPassword'
      }, { status: 400 });
    }

    const result = await discoverWordPressContent(wpSite, wpUsername, wpPassword);

    return NextResponse.json(result);
  } catch (error) {
    console.error('WordPress content discovery error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
