
import {
  retryWithBackoff,
  generateUniqueSuffix,
  sanitizeFilename,
  getFileExtension,
  getMimeTypeFromExtension,
  createProgressTracker
} from './utils.js';
import { 
  createWPClient, 
  fetchAllPosts, 
  fetchAllPages, 
  fetchAllMedia,
  downloadMediaFile,
  extractPostTaxonomies,
  prepareContentForMigration
} from './wordpress.js';
import {
  supabaseAdmin,
  createMigrationTables,
  createMediaBucket,
  uploadMediaFile,
  logMigrationProgress,
  createMigrationJob,
  getMigrationJob
} from './supabase.js';

// Simple logger
const logger = {
  info: (message, meta = {}) => console.log(`[INFO] ${message}`, meta),
  error: (message, meta = {}) => console.error(`[ERROR] ${message}`, meta),
  warn: (message, meta = {}) => console.warn(`[WARN] ${message}`, meta)
};

export class WordPressMigrator {
  constructor(wpConfig) {
    this.wpConfig = wpConfig;
    this.wp = createWPClient(wpConfig.siteUrl, wpConfig.username, wpConfig.password);
    this.suffix = generateUniqueSuffix();
    this.concurrency = 3; // Limit concurrent operations
    this.mediaMapping = new Map(); // Track old URL -> new URL mappings
    this.jobId = null;
    this.progress = {
      posts: { total: 0, completed: 0 },
      pages: { total: 0, completed: 0 },
      media: { total: 0, completed: 0 },
      status: 'initializing'
    };
    this.progressTracker = createProgressTracker();
    this.errors = [];
  }

  async initialize() {
    try {
      logger.info('Initializing migration', { 
        domain: this.wpConfig.siteUrl,
        suffix: this.suffix 
      });

      // Create migration tables
      const tableResults = await createMigrationTables(this.suffix);
      logger.info('Migration tables created', { results: tableResults });

      // Create media bucket
      const { bucketName } = await createMediaBucket(this.suffix);
      this.bucketName = bucketName;
      logger.info('Media bucket created', { bucketName });

      // Create migration job record
      const job = await createMigrationJob(
        this.wpConfig.siteUrl, 
        this.suffix, 
        0 // Will update with actual count
      );
      this.jobId = job.id;
      logger.info('Migration job created', { jobId: this.jobId });

      return { success: true, jobId: this.jobId, suffix: this.suffix };
    } catch (error) {
      logger.error('Migration initialization failed', { error: error.message });
      throw error;
    }
  }

  async startMigration() {
    try {
      this.progress.status = 'discovering';
      await this.updateProgress();

      // Discover content
      const content = await this.discoverContent();
      
      this.progress.posts.total = content.posts.length;
      this.progress.pages.total = content.pages.length;
      this.progress.media.total = content.media.length;
      this.progress.status = 'migrating';
      
      await this.updateProgress();

      // Start migration process
      await this.migrateContent(content);

      this.progress.status = 'completed';
      await this.updateProgress();

      logger.info('Migration completed successfully', {
        jobId: this.jobId,
        suffix: this.suffix,
        totals: {
          posts: this.progress.posts.completed,
          pages: this.progress.pages.completed,
          media: this.progress.media.completed
        }
      });

      return {
        success: true,
        jobId: this.jobId,
        suffix: this.suffix,
        summary: this.progress
      };

    } catch (error) {
      this.progress.status = 'failed';
      await this.updateProgress();
      logger.error('Migration failed', { error: error.message, jobId: this.jobId });
      throw error;
    }
  }

  async discoverContent() {
    logger.info('Discovering WordPress content');

    const [posts, pages, media] = await Promise.all([
      fetchAllPosts(this.wp, (progress) => {
        logger.info('Fetching posts', progress);
      }),
      fetchAllPages(this.wp, (progress) => {
        logger.info('Fetching pages', progress);
      }),
      fetchAllMedia(this.wp, (progress) => {
        logger.info('Fetching media', progress);
      })
    ]);

    logger.info('Content discovery completed', {
      posts: posts.length,
      pages: pages.length,
      media: media.length
    });

    return { posts, pages, media };
  }

  async migrateContent(content) {
    // First migrate media files (so we can update URLs in posts/pages)
    if (content.media.length > 0) {
      await this.migrateMedia(content.media);
    }

    // Then migrate posts and pages with updated media URLs
    const migrationTasks = [
      ...content.posts.map(post => () => this.migratePost(post)),
      ...content.pages.map(page => () => this.migratePage(page))
    ];

    // Add all tasks to queue
    migrationTasks.forEach(task => this.queue.add(task));

    // Wait for all migrations to complete
    await this.queue.onIdle();
  }

  async migrateMedia(mediaItems) {
    logger.info('Starting media migration', { count: mediaItems.length });

    for (const media of mediaItems) {
      try {
        await this.queue.add(() => this.migrateMediaItem(media));
      } catch (error) {
        logger.error('Failed to queue media item', { 
          mediaId: media.id, 
          error: error.message 
        });
      }
    }

    await this.queue.onIdle();
    logger.info('Media migration completed');
  }

  async migrateMediaItem(media) {
    try {
      // Download the media file with retry
      const { buffer, contentType, size } = await retryWithBackoff(async () => {
        return await downloadMediaFile(media.source_url);
      }, 3, 2000);

      // Generate safe filename
      const extension = getFileExtension(media.source_url);
      const baseFilename = media.slug || `media-${media.id}`;
      const filename = sanitizeFilename(`${media.id}-${baseFilename}.${extension}`);

      // Upload to Supabase with retry
      const { publicUrl } = await retryWithBackoff(async () => {
        return await uploadMediaFile(
          this.bucketName,
          filename,
          buffer,
          contentType || getMimeTypeFromExtension(extension)
        );
      }, 3, 1000);

      // Store URL mapping
      this.mediaMapping.set(media.source_url, publicUrl);

      // Save media record with retry
      await retryWithBackoff(async () => {
        const { error } = await supabaseAdmin
          .from(`wp_media_${this.suffix}`)
          .insert({
            wp_id: media.id,
            title: media.title?.rendered || '',
            filename: filename,
            original_url: media.source_url,
            supabase_url: publicUrl,
            mime_type: media.mime_type || getMimeTypeFromExtension(extension),
            file_size: size,
            width: media.media_details?.width,
            height: media.media_details?.height,
            alt_text: media.alt_text || '',
            caption: media.caption?.rendered || '',
            created_at: media.date
          });

        if (error) throw error;
      }, 2, 1000);

      this.progress.media.completed++;
      this.progressTracker.increment();
      await this.updateProgress();

      logger.info('Media migrated', {
        mediaId: media.id,
        filename,
        originalUrl: media.source_url,
        newUrl: publicUrl
      });

    } catch (error) {
      this.errors.push({
        type: 'media',
        id: media.id,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      this.progressTracker.fail();

      logger.error('Media migration failed', {
        mediaId: media.id,
        error: error.message
      });
    }
  }

  async migratePost(post) {
    try {
      const { categories, tags } = extractPostTaxonomies(post);
      const cleanContent = prepareContentForMigration(
        post.content?.rendered || '', 
        Object.fromEntries(this.mediaMapping)
      );

      await supabaseAdmin
        .from(`wp_posts_${this.suffix}`)
        .insert({
          wp_id: post.id,
          title: post.title?.rendered || '',
          content: cleanContent,
          excerpt: post.excerpt?.rendered || '',
          slug: post.slug,
          status: post.status,
          type: post.type,
          author_id: post.author,
          featured_media_id: post.featured_media,
          categories: categories,
          tags: tags,
          created_at: post.date,
          modified_at: post.modified
        });

      this.progress.posts.completed++;
      await this.updateProgress();

      logger.info('Post migrated', { postId: post.id, title: post.title?.rendered });

    } catch (error) {
      logger.error('Post migration failed', { 
        postId: post.id, 
        error: error.message 
      });
    }
  }

  async migratePage(page) {
    try {
      const cleanContent = prepareContentForMigration(
        page.content?.rendered || '', 
        Object.fromEntries(this.mediaMapping)
      );

      await supabaseAdmin
        .from(`wp_pages_${this.suffix}`)
        .insert({
          wp_id: page.id,
          title: page.title?.rendered || '',
          content: cleanContent,
          slug: page.slug,
          status: page.status,
          parent_id: page.parent,
          template: page.template,
          featured_media_id: page.featured_media,
          created_at: page.date,
          modified_at: page.modified
        });

      this.progress.pages.completed++;
      await this.updateProgress();

      logger.info('Page migrated', { pageId: page.id, title: page.title?.rendered });

    } catch (error) {
      logger.error('Page migration failed', { 
        pageId: page.id, 
        error: error.message 
      });
    }
  }

  async updateProgress() {
    if (this.jobId) {
      try {
        await logMigrationProgress(this.jobId, {
          ...this.progress,
          completed: this.progress.posts.completed + this.progress.pages.completed + this.progress.media.completed,
          checkpoint: {
            mediaMapping: Object.fromEntries(this.mediaMapping),
            suffix: this.suffix,
            bucketName: this.bucketName
          }
        });
      } catch (error) {
        logger.error('Failed to update progress', { error: error.message });
      }
    }
  }

  getFileExtension(url) {
    const match = url.match(/\.([^.?]+)(?:\?|$)/);
    return match ? match[1] : 'jpg';
  }

  // Resume migration from checkpoint
  static async resumeMigration(jobId) {
    try {
      logger.info('Resuming migration', { jobId });

      // Get the migration job
      const job = await getMigrationJob(jobId);
      if (!job) {
        throw new Error('Migration job not found');
      }

      if (job.status === 'completed') {
        logger.info('Migration already completed', { jobId });
        return { success: true, message: 'Migration already completed' };
      }

      // Extract checkpoint data
      const checkpoint = job.last_checkpoint || {};
      const wpConfig = {
        siteUrl: job.domain,
        username: checkpoint.username,
        password: checkpoint.password
      };

      if (!wpConfig.username || !wpConfig.password) {
        throw new Error('Cannot resume migration: missing WordPress credentials in checkpoint');
      }

      // Create new migrator instance with existing suffix
      const migrator = new WordPressMigrator(wpConfig);
      migrator.suffix = job.suffix;
      migrator.jobId = jobId;
      migrator.bucketName = checkpoint.bucketName || `wp-media-${job.suffix}`;

      // Restore media mapping from checkpoint
      if (checkpoint.mediaMapping) {
        migrator.mediaMapping = new Map(Object.entries(checkpoint.mediaMapping));
      }

      // Restore progress from job
      if (job.progress) {
        migrator.progress = {
          ...migrator.progress,
          ...job.progress
        };
      }

      logger.info('Resuming migration from checkpoint', {
        jobId,
        suffix: migrator.suffix,
        currentProgress: migrator.progress
      });

      // Continue migration
      const result = await migrator.startMigration();
      return result;

    } catch (error) {
      logger.error('Failed to resume migration', { jobId, error: error.message });
      throw error;
    }
  }
}
