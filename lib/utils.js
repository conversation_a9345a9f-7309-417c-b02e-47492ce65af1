/**
 * Utility functions for WordPress to Supabase migration
 */

// Retry function with exponential backoff
export async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`Attempt ${attempt} failed, retrying in ${delay}ms...`, error.message);
      await sleep(delay);
    }
  }
  
  throw lastError;
}

// Sleep utility
export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Validate WordPress URL
export function validateWordPressUrl(url) {
  try {
    const parsedUrl = new URL(url);
    
    // Must be HTTP or HTTPS
    if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
      return { valid: false, error: 'U<PERSON> must use HTTP or HTTPS protocol' };
    }
    
    // Must have a hostname
    if (!parsedUrl.hostname) {
      return { valid: false, error: 'Invalid hostname' };
    }
    
    return { valid: true };
  } catch {
    return { valid: false, error: 'Invalid URL format' };
  }
}

// Validate WordPress credentials
export function validateWordPressCredentials(credentials) {
  const { wpSite, wpUsername, wpPassword } = credentials;
  
  if (!wpSite || !wpUsername || !wpPassword) {
    return { 
      valid: false, 
      error: 'All fields are required: WordPress site, username, and app password' 
    };
  }
  
  const urlValidation = validateWordPressUrl(wpSite);
  if (!urlValidation.valid) {
    return { valid: false, error: `Invalid WordPress URL: ${urlValidation.error}` };
  }
  
  if (wpUsername.length < 2) {
    return { valid: false, error: 'Username must be at least 2 characters long' };
  }
  
  if (wpPassword.length < 10) {
    return { valid: false, error: 'App password seems too short. Make sure to use a WordPress App Password, not your regular password.' };
  }
  
  return { valid: true };
}

// Sanitize filename for storage
export function sanitizeFilename(filename) {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .toLowerCase();
}

// Get file extension from URL or filename
export function getFileExtension(urlOrFilename) {
  const match = urlOrFilename.match(/\.([^.?#]+)(?:[?#]|$)/);
  return match ? match[1].toLowerCase() : 'unknown';
}

// Get MIME type from file extension
export function getMimeTypeFromExtension(extension) {
  const mimeTypes = {
    // Images
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'bmp': 'image/bmp',
    'ico': 'image/x-icon',
    
    // Documents
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    
    // Audio
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav',
    'ogg': 'audio/ogg',
    'm4a': 'audio/mp4',
    
    // Video
    'mp4': 'video/mp4',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'wmv': 'video/x-ms-wmv',
    'flv': 'video/x-flv',
    'webm': 'video/webm',
    
    // Archives
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed',
    'tar': 'application/x-tar',
    'gz': 'application/gzip',
    
    // Text
    'txt': 'text/plain',
    'csv': 'text/csv',
    'json': 'application/json',
    'xml': 'application/xml',
    'html': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript'
  };
  
  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}

// Format file size for display
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Generate unique suffix for tables/buckets
export function generateUniqueSuffix() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Clean HTML content for migration
export function cleanHtmlContent(html) {
  if (!html) return '';
  
  // Remove WordPress-specific shortcodes that won't work in static sites
  let cleaned = html
    .replace(/\[gallery[^\]]*\]/g, '<!-- Gallery removed during migration -->')
    .replace(/\[caption[^\]]*\](.*?)\[\/caption\]/gs, '$1')
    .replace(/\[embed[^\]]*\](.*?)\[\/embed\]/gs, '$1')
    .replace(/\[audio[^\]]*\]/g, '<!-- Audio shortcode removed -->')
    .replace(/\[video[^\]]*\]/g, '<!-- Video shortcode removed -->')
    .replace(/\[playlist[^\]]*\]/g, '<!-- Playlist shortcode removed -->');
  
  // Clean up excessive whitespace
  cleaned = cleaned
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim();
  
  return cleaned;
}

// Validate Supabase configuration
export function validateSupabaseConfig() {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    return {
      valid: false,
      error: `Missing required environment variables: ${missing.join(', ')}`
    };
  }
  
  // Validate URL format
  try {
    new URL(process.env.NEXT_PUBLIC_SUPABASE_URL);
  } catch {
    return {
      valid: false,
      error: 'Invalid NEXT_PUBLIC_SUPABASE_URL format'
    };
  }
  
  // Basic JWT validation for service role key
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!serviceKey.startsWith('eyJ') || serviceKey.split('.').length !== 3) {
    return {
      valid: false,
      error: 'Invalid SUPABASE_SERVICE_ROLE_KEY format (should be a JWT token)'
    };
  }
  
  return { valid: true };
}

// Create progress tracker
export function createProgressTracker(totalItems = 0) {
  return {
    total: totalItems,
    completed: 0,
    failed: 0,
    startTime: Date.now(),
    
    increment() {
      this.completed++;
    },
    
    fail() {
      this.failed++;
    },
    
    getPercentage() {
      return this.total > 0 ? Math.round((this.completed / this.total) * 100) : 0;
    },
    
    getElapsedTime() {
      return Date.now() - this.startTime;
    },
    
    getEstimatedTimeRemaining() {
      if (this.completed === 0) return null;
      
      const elapsedTime = this.getElapsedTime();
      const avgTimePerItem = elapsedTime / this.completed;
      const remainingItems = this.total - this.completed;
      
      return Math.round(avgTimePerItem * remainingItems);
    },
    
    getSummary() {
      return {
        total: this.total,
        completed: this.completed,
        failed: this.failed,
        percentage: this.getPercentage(),
        elapsedTime: this.getElapsedTime(),
        estimatedTimeRemaining: this.getEstimatedTimeRemaining()
      };
    }
  };
}

// Log levels
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
};

// Simple logger for client-side
export function createLogger(context = 'Migration') {
  return {
    error: (message, data = {}) => console.error(`[${context}] ERROR:`, message, data),
    warn: (message, data = {}) => console.warn(`[${context}] WARN:`, message, data),
    info: (message, data = {}) => console.info(`[${context}] INFO:`, message, data),
    debug: (message, data = {}) => console.debug(`[${context}] DEBUG:`, message, data)
  };
}
