// WordPress API client using fetch for Next.js compatibility

// Create WordPress API client configuration
export function createWPClient(siteUrl, username, password) {
  const endpoint = siteUrl.endsWith('/') ? `${siteUrl}wp-json` : `${siteUrl}/wp-json`;

  return {
    endpoint,
    username,
    password,
    auth: Buffer.from(`${username}:${password}`).toString('base64')
  };
}

// Test WordPress connection
export async function testWordPressConnection(siteUrl, username, password) {
  try {
    const wp = createWPClient(siteUrl, username, password);

    // Try to fetch a single post to test connection
    const response = await fetch(`${wp.endpoint}/wp/v2/posts?per_page=1`, {
      headers: {
        'Authorization': `Basic ${wp.auth}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const posts = await response.json();

    return {
      success: true,
      message: 'Connection successful',
      data: {
        postsFound: posts.length > 0,
        siteUrl: siteUrl
      }
    };
  } catch (error) {
    let errorMessage = 'Connection failed';

    // Provide more specific error messages
    if (error.message.includes('401')) {
      errorMessage = 'Authentication failed. Please check your username and app password.';
    } else if (error.message.includes('404')) {
      errorMessage = 'WordPress REST API not found. Please check the site URL and ensure the REST API is enabled.';
    } else if (error.message.includes('403')) {
      errorMessage = 'Access forbidden. Please check your app password permissions.';
    } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      errorMessage = 'Cannot reach the WordPress site. Please check the URL and your internet connection.';
    }

    return {
      success: false,
      message: errorMessage,
      error: error.message
    };
  }
}

// Discover all WordPress content
export async function discoverWordPressContent(siteUrl, username, password) {
  try {
    const wp = createWPClient(siteUrl, username, password);

    // Helper function to get count from WordPress API
    const getCount = async (endpoint) => {
      const response = await fetch(`${wp.endpoint}/wp/v2/${endpoint}?per_page=1`, {
        headers: {
          'Authorization': `Basic ${wp.auth}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        return { count: 0, sample: null };
      }

      const data = await response.json();
      const totalHeader = response.headers.get('X-WP-Total');
      const count = totalHeader ? parseInt(totalHeader) : data.length;

      return { count, sample: data[0] || null };
    };

    // Get content counts
    const [posts, pages, media, categories, tags] = await Promise.all([
      getCount('posts'),
      getCount('pages'),
      getCount('media'),
      getCount('categories'),
      getCount('tags')
    ]);

    const totalItems = posts.count + pages.count + media.count;

    return {
      success: true,
      content: {
        posts: posts.count,
        pages: pages.count,
        media: media.count,
        categories: categories.count,
        tags: tags.count,
        total: totalItems
      },
      samples: {
        post: posts.sample,
        page: pages.sample,
        media: media.sample
      }
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'Failed to discover content',
      error: error
    };
  }
}

// Fetch all posts with pagination
export async function fetchAllPosts(wp, onProgress = null) {
  const allPosts = [];
  let page = 1;
  const perPage = 50; // Reasonable batch size
  
  try {
    while (true) {
      const posts = await wp.posts()
        .perPage(perPage)
        .page(page)
        .embed(); // Include embedded data like featured media
      
      if (posts.length === 0) break;
      
      allPosts.push(...posts);
      
      if (onProgress) {
        onProgress({
          type: 'posts',
          current: allPosts.length,
          batch: posts.length,
          page
        });
      }
      
      page++;
      
      // Prevent infinite loops
      if (page > 1000) {
        console.warn('Reached maximum page limit for posts');
        break;
      }
    }
  } catch (error) {
    console.error('Error fetching posts:', error);
    throw error;
  }
  
  return allPosts;
}

// Fetch all pages with pagination
export async function fetchAllPages(wp, onProgress = null) {
  const allPages = [];
  let page = 1;
  const perPage = 50;
  
  try {
    while (true) {
      const pages = await wp.pages()
        .perPage(perPage)
        .page(page)
        .embed();
      
      if (pages.length === 0) break;
      
      allPages.push(...pages);
      
      if (onProgress) {
        onProgress({
          type: 'pages',
          current: allPages.length,
          batch: pages.length,
          page
        });
      }
      
      page++;
      
      if (page > 1000) {
        console.warn('Reached maximum page limit for pages');
        break;
      }
    }
  } catch (error) {
    console.error('Error fetching pages:', error);
    throw error;
  }
  
  return allPages;
}

// Fetch all media with pagination
export async function fetchAllMedia(wp, onProgress = null) {
  const allMedia = [];
  let page = 1;
  const perPage = 50;
  
  try {
    while (true) {
      const media = await wp.media()
        .perPage(perPage)
        .page(page);
      
      if (media.length === 0) break;
      
      allMedia.push(...media);
      
      if (onProgress) {
        onProgress({
          type: 'media',
          current: allMedia.length,
          batch: media.length,
          page
        });
      }
      
      page++;
      
      if (page > 1000) {
        console.warn('Reached maximum page limit for media');
        break;
      }
    }
  } catch (error) {
    console.error('Error fetching media:', error);
    throw error;
  }
  
  return allMedia;
}

// Download media file from WordPress
export async function downloadMediaFile(mediaUrl) {
  try {
    const response = await fetch(mediaUrl);
    if (!response.ok) {
      throw new Error(`Failed to download media: ${response.statusText}`);
    }
    
    const buffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'application/octet-stream';
    
    return {
      buffer: Buffer.from(buffer),
      contentType,
      size: buffer.byteLength
    };
  } catch (error) {
    console.error('Error downloading media file:', error);
    throw error;
  }
}

// Extract categories and tags from post
export function extractPostTaxonomies(post) {
  const categories = [];
  const tags = [];
  
  // Extract from embedded data if available
  if (post._embedded) {
    if (post._embedded['wp:term']) {
      post._embedded['wp:term'].forEach(termGroup => {
        termGroup.forEach(term => {
          if (term.taxonomy === 'category') {
            categories.push(term.name);
          } else if (term.taxonomy === 'post_tag') {
            tags.push(term.name);
          }
        });
      });
    }
  }
  
  return { categories, tags };
}

// Clean and prepare content for migration
export function prepareContentForMigration(content, mediaMapping = {}) {
  if (!content) return '';

  // First clean the HTML content
  let cleanContent = cleanHtmlContent(content);

  // Replace WordPress media URLs with Supabase URLs
  Object.entries(mediaMapping).forEach(([wpUrl, supabaseUrl]) => {
    const regex = new RegExp(wpUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    cleanContent = cleanContent.replace(regex, supabaseUrl);
  });

  return cleanContent;
}

// Get WordPress site info
export async function getWordPressSiteInfo(wp) {
  try {
    // This endpoint might not be available on all WordPress sites
    const siteInfo = await wp.root();
    return {
      name: siteInfo.name || 'Unknown',
      description: siteInfo.description || '',
      url: siteInfo.url || '',
      timezone: siteInfo.timezone_string || 'UTC'
    };
  } catch (error) {
    console.warn('Could not fetch site info:', error.message);
    return {
      name: 'WordPress Site',
      description: '',
      url: '',
      timezone: 'UTC'
    };
  }
}
