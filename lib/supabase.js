import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Client for server-side operations with service role
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Client for client-side operations
export const supabase = createClient(
  supabaseUrl, 
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

// Create migration tables with unique suffix using direct SQL
export async function createMigrationTables(suffix) {
  try {
    const tables = [];

    // Create posts table
    const postsTableName = `wp_posts_${suffix}`;
    const { error: postsError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.${postsTableName} (
          id BIGSERIAL PRIMARY KEY,
          wp_id BIGINT UNIQUE NOT NULL,
          title TEXT,
          content TEXT,
          excerpt TEXT,
          status VARCHAR(20) DEFAULT 'publish',
          type VARCHAR(20) DEFAULT 'post',
          slug TEXT,
          author_id BIGINT,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          published_at TIMESTAMPTZ,
          featured_image_url TEXT,
          meta_data JSONB DEFAULT '{}'::jsonb
        );

        CREATE INDEX IF NOT EXISTS idx_${postsTableName}_wp_id ON public.${postsTableName}(wp_id);
        CREATE INDEX IF NOT EXISTS idx_${postsTableName}_status ON public.${postsTableName}(status);
        CREATE INDEX IF NOT EXISTS idx_${postsTableName}_type ON public.${postsTableName}(type);

        ALTER TABLE public.${postsTableName} ENABLE ROW LEVEL SECURITY;

        CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON public.${postsTableName}
          FOR SELECT USING (true);
        CREATE POLICY IF NOT EXISTS "Enable insert for authenticated users only" ON public.${postsTableName}
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');
        CREATE POLICY IF NOT EXISTS "Enable update for authenticated users only" ON public.${postsTableName}
          FOR UPDATE USING (auth.role() = 'authenticated');
      `
    });

    if (!postsError) {
      tables.push(postsTableName);
    }

    // Create pages table
    const pagesTableName = `wp_pages_${suffix}`;
    const { error: pagesError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.${pagesTableName} (
          id BIGSERIAL PRIMARY KEY,
          wp_id BIGINT UNIQUE NOT NULL,
          title TEXT,
          content TEXT,
          excerpt TEXT,
          status VARCHAR(20) DEFAULT 'publish',
          slug TEXT,
          parent_id BIGINT,
          template TEXT,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          published_at TIMESTAMPTZ,
          featured_image_url TEXT,
          meta_data JSONB DEFAULT '{}'::jsonb
        );

        CREATE INDEX IF NOT EXISTS idx_${pagesTableName}_wp_id ON public.${pagesTableName}(wp_id);
        CREATE INDEX IF NOT EXISTS idx_${pagesTableName}_status ON public.${pagesTableName}(status);
        CREATE INDEX IF NOT EXISTS idx_${pagesTableName}_parent_id ON public.${pagesTableName}(parent_id);

        ALTER TABLE public.${pagesTableName} ENABLE ROW LEVEL SECURITY;

        CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON public.${pagesTableName}
          FOR SELECT USING (true);
        CREATE POLICY IF NOT EXISTS "Enable insert for authenticated users only" ON public.${pagesTableName}
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');
        CREATE POLICY IF NOT EXISTS "Enable update for authenticated users only" ON public.${pagesTableName}
          FOR UPDATE USING (auth.role() = 'authenticated');
      `
    });

    if (!pagesError) {
      tables.push(pagesTableName);
    }

    // Create media table
    const mediaTableName = `wp_media_${suffix}`;
    const { error: mediaError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.${mediaTableName} (
          id BIGSERIAL PRIMARY KEY,
          wp_id BIGINT UNIQUE NOT NULL,
          title TEXT,
          filename TEXT,
          original_url TEXT,
          supabase_url TEXT,
          mime_type TEXT,
          file_size BIGINT,
          alt_text TEXT,
          caption TEXT,
          description TEXT,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          meta_data JSONB DEFAULT '{}'::jsonb
        );

        CREATE INDEX IF NOT EXISTS idx_${mediaTableName}_wp_id ON public.${mediaTableName}(wp_id);
        CREATE INDEX IF NOT EXISTS idx_${mediaTableName}_mime_type ON public.${mediaTableName}(mime_type);

        ALTER TABLE public.${mediaTableName} ENABLE ROW LEVEL SECURITY;

        CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON public.${mediaTableName}
          FOR SELECT USING (true);
        CREATE POLICY IF NOT EXISTS "Enable insert for authenticated users only" ON public.${mediaTableName}
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');
        CREATE POLICY IF NOT EXISTS "Enable update for authenticated users only" ON public.${mediaTableName}
          FOR UPDATE USING (auth.role() = 'authenticated');
      `
    });

    if (!mediaError) {
      tables.push(mediaTableName);
    }

    return {
      success: true,
      tables,
      error: null
    };
  } catch (err) {
    console.error('Failed to create migration tables:', err);
    return {
      success: false,
      tables: [],
      error: err.message
    };
  }
}

// RLS policies are now handled by the create_migration_tables SQL function
// This function is kept for backward compatibility but does nothing
export async function setupRLSPolicies(suffix) {
  console.log(`RLS policies for suffix ${suffix} are automatically created by create_migration_tables function`);
  return { success: true, message: 'RLS policies handled by SQL function' };
}

// Create storage bucket for media files
export async function createMediaBucket(suffix) {
  const bucketName = `wp-media-${suffix}`;
  
  const { data, error } = await supabaseAdmin.storage.createBucket(bucketName, {
    public: true,
    allowedMimeTypes: ['image/*', 'video/*', 'audio/*', 'application/pdf'],
    fileSizeLimit: 50 * 1024 * 1024 // 50MB
  });

  if (error && error.message !== 'Bucket already exists') {
    throw error;
  }

  return { bucketName, data, error };
}

// Upload media file to Supabase storage
export async function uploadMediaFile(bucketName, fileName, fileBuffer, mimeType) {
  const { data, error } = await supabaseAdmin.storage
    .from(bucketName)
    .upload(fileName, fileBuffer, {
      contentType: mimeType,
      upsert: true
    });

  if (error) throw error;

  // Get public URL
  const { data: urlData } = supabaseAdmin.storage
    .from(bucketName)
    .getPublicUrl(fileName);

  return {
    path: data.path,
    publicUrl: urlData.publicUrl
  };
}

// Log migration progress
export async function logMigrationProgress(jobId, progress) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .update({
      progress,
      completed_items: progress.completed || 0,
      last_checkpoint: progress.checkpoint || {},
      status: progress.status || 'running'
    })
    .eq('id', jobId);

  if (error) throw error;
  return data;
}

// Get migration job status
export async function getMigrationJob(jobId) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .select('*')
    .eq('id', jobId)
    .single();

  if (error) throw error;
  return data;
}

// Create new migration job
export async function createMigrationJob(domain, suffix, totalItems = 0) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .insert({
      domain,
      suffix,
      total_items: totalItems,
      status: 'pending'
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

// Get migration summary using direct queries
export async function getMigrationSummary(suffix) {
  try {
    let posts = 0, pages = 0, media = 0;

    // Count posts
    try {
      const { count: postsCount } = await supabaseAdmin
        .from(`wp_posts_${suffix}`)
        .select('*', { count: 'exact', head: true });
      posts = postsCount || 0;
    } catch (err) {
      console.warn('Could not count posts:', err.message);
    }

    // Count pages
    try {
      const { count: pagesCount } = await supabaseAdmin
        .from(`wp_pages_${suffix}`)
        .select('*', { count: 'exact', head: true });
      pages = pagesCount || 0;
    } catch (err) {
      console.warn('Could not count pages:', err.message);
    }

    // Count media
    try {
      const { count: mediaCount } = await supabaseAdmin
        .from(`wp_media_${suffix}`)
        .select('*', { count: 'exact', head: true });
      media = mediaCount || 0;
    } catch (err) {
      console.warn('Could not count media:', err.message);
    }

    // Get bucket info
    let mediaFiles = 0;
    try {
      const { data: buckets } = await supabaseAdmin.storage.listBuckets();
      const mediaBucket = buckets?.find(b => b.name.includes(suffix));
      if (mediaBucket) {
        const { data: files } = await supabaseAdmin.storage
          .from(mediaBucket.name)
          .list();
        mediaFiles = files?.length || 0;
      }
    } catch (err) {
      console.warn('Could not get bucket info:', err.message);
    }

    return {
      posts,
      pages,
      media,
      total: posts + pages + media,
      media_files: mediaFiles,
      bucket_name: `wp-media-${suffix}`,
      suffix
    };
  } catch (err) {
    console.error('Failed to get migration summary:', err);
    return {
      posts: 0,
      pages: 0,
      media: 0,
      total: 0,
      media_files: 0,
      error: err.message,
      suffix
    };
  }
}
