import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Client for server-side operations with service role
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Client for client-side operations
export const supabase = createClient(
  supabaseUrl, 
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

// Create migration tables with unique suffix - simplified approach
export async function createMigrationTables(suffix) {
  try {
    console.log(`Creating migration tables with suffix: ${suffix}`);

    const tables = [];

    // Try to create a simple test record in each table to verify they exist
    // If they don't exist, we'll get an error but continue

    const postsTableName = `wp_posts_${suffix}`;
    const pagesTableName = `wp_pages_${suffix}`;
    const mediaTableName = `wp_media_${suffix}`;

    // Test posts table
    try {
      await supabaseAdmin
        .from(postsTableName)
        .select('id')
        .limit(1);
      tables.push(postsTableName);
      console.log(`Table ${postsTableName} is accessible`);
    } catch (err) {
      console.log(`Table ${postsTableName} may not exist yet: ${err.message}`);
      // We'll still add it to the list as it will be created during migration
      tables.push(postsTableName);
    }

    // Test pages table
    try {
      await supabaseAdmin
        .from(pagesTableName)
        .select('id')
        .limit(1);
      tables.push(pagesTableName);
      console.log(`Table ${pagesTableName} is accessible`);
    } catch (err) {
      console.log(`Table ${pagesTableName} may not exist yet: ${err.message}`);
      tables.push(pagesTableName);
    }

    // Test media table
    try {
      await supabaseAdmin
        .from(mediaTableName)
        .select('id')
        .limit(1);
      tables.push(mediaTableName);
      console.log(`Table ${mediaTableName} is accessible`);
    } catch (err) {
      console.log(`Table ${mediaTableName} may not exist yet: ${err.message}`);
      tables.push(mediaTableName);
    }

    // Test migration_jobs table
    try {
      await supabaseAdmin
        .from('migration_jobs')
        .select('id')
        .limit(1);
      console.log('migration_jobs table is accessible');
    } catch (err) {
      console.warn('migration_jobs table may not exist:', err.message);
    }

    return {
      success: true,
      tables: [...new Set(tables)], // Remove duplicates
      error: null
    };
  } catch (err) {
    console.error('Failed to create migration tables:', err);
    return {
      success: false,
      tables: [],
      error: err.message
    };
  }
}

// RLS policies are now handled by the create_migration_tables SQL function
// This function is kept for backward compatibility but does nothing
export async function setupRLSPolicies(suffix) {
  console.log(`RLS policies for suffix ${suffix} are automatically created by create_migration_tables function`);
  return { success: true, message: 'RLS policies handled by SQL function' };
}

// Create storage bucket for media files
export async function createMediaBucket(suffix) {
  const bucketName = `wp-media-${suffix}`;
  
  const { data, error } = await supabaseAdmin.storage.createBucket(bucketName, {
    public: true,
    allowedMimeTypes: ['image/*', 'video/*', 'audio/*', 'application/pdf'],
    fileSizeLimit: 50 * 1024 * 1024 // 50MB
  });

  if (error && error.message !== 'Bucket already exists') {
    throw error;
  }

  return { bucketName, data, error };
}

// Upload media file to Supabase storage
export async function uploadMediaFile(bucketName, fileName, fileBuffer, mimeType) {
  const { data, error } = await supabaseAdmin.storage
    .from(bucketName)
    .upload(fileName, fileBuffer, {
      contentType: mimeType,
      upsert: true
    });

  if (error) throw error;

  // Get public URL
  const { data: urlData } = supabaseAdmin.storage
    .from(bucketName)
    .getPublicUrl(fileName);

  return {
    path: data.path,
    publicUrl: urlData.publicUrl
  };
}

// Log migration progress
export async function logMigrationProgress(jobId, progress) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .update({
      progress,
      completed_items: progress.completed || 0,
      last_checkpoint: progress.checkpoint || {},
      status: progress.status || 'running'
    })
    .eq('id', jobId);

  if (error) throw error;
  return data;
}

// Get migration job status
export async function getMigrationJob(jobId) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .select('*')
    .eq('id', jobId)
    .single();

  if (error) throw error;
  return data;
}

// Create new migration job
export async function createMigrationJob(domain, suffix, totalItems = 0) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .insert({
      domain,
      suffix,
      total_items: totalItems,
      status: 'pending'
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

// Get migration summary using direct queries
export async function getMigrationSummary(suffix) {
  try {
    let posts = 0, pages = 0, media = 0;

    // Count posts
    try {
      const { count: postsCount } = await supabaseAdmin
        .from(`wp_posts_${suffix}`)
        .select('*', { count: 'exact', head: true });
      posts = postsCount || 0;
    } catch (err) {
      console.warn('Could not count posts:', err.message);
    }

    // Count pages
    try {
      const { count: pagesCount } = await supabaseAdmin
        .from(`wp_pages_${suffix}`)
        .select('*', { count: 'exact', head: true });
      pages = pagesCount || 0;
    } catch (err) {
      console.warn('Could not count pages:', err.message);
    }

    // Count media
    try {
      const { count: mediaCount } = await supabaseAdmin
        .from(`wp_media_${suffix}`)
        .select('*', { count: 'exact', head: true });
      media = mediaCount || 0;
    } catch (err) {
      console.warn('Could not count media:', err.message);
    }

    // Get bucket info
    let mediaFiles = 0;
    try {
      const { data: buckets } = await supabaseAdmin.storage.listBuckets();
      const mediaBucket = buckets?.find(b => b.name.includes(suffix));
      if (mediaBucket) {
        const { data: files } = await supabaseAdmin.storage
          .from(mediaBucket.name)
          .list();
        mediaFiles = files?.length || 0;
      }
    } catch (err) {
      console.warn('Could not get bucket info:', err.message);
    }

    return {
      posts,
      pages,
      media,
      total: posts + pages + media,
      media_files: mediaFiles,
      bucket_name: `wp-media-${suffix}`,
      suffix
    };
  } catch (err) {
    console.error('Failed to get migration summary:', err);
    return {
      posts: 0,
      pages: 0,
      media: 0,
      total: 0,
      media_files: 0,
      error: err.message,
      suffix
    };
  }
}
